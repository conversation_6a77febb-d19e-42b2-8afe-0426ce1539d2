import { useEffect, useState, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { TimerDisplay } from "./TimerDisplay";
import { TimerControls } from "./TimerControls";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { saveStudySession, getExtendedUserProfile, updateUserProfile } from "@/utils/supabase";
import { toast } from "@/components/ui/use-toast";
import { Settings, PictureInPicture, PictureInPicture2, BellRing, BellOff } from "lucide-react"; // Added Bell icons
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SubjectManager, Subject } from "./SubjectManager";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
// Removed worker-timers import


// --- Notification Permission ---
type NotificationPermissionStatus = "default" | "granted" | "denied";


// Safari-specific PiP API declarations
declare global {
  interface HTMLVideoElement {
    webkitSupportsPresentationMode?: (mode: string) => boolean;
    webkitPresentationMode?: string;
    webkitSetPresentationMode?: (mode: string) => void;
  }

  interface HTMLCanvasElement {
    webkitCaptureStream?: () => MediaStream;
  }
}

interface TimerSettings {
  workDuration: number; // seconds
  shortBreakDuration: number; // seconds
  longBreakDuration: number; // seconds
  sessionsUntilLongBreak: number;
  notificationInterval: number; // minutes
  dayResetHour: number; // Hour (0-23) when the day resets for logging
}

type TimerStatus = "idle" | "running" | "paused";
type TimerMode = "pomodoro" | "stopwatch";
type PhaseType = "work" | "shortBreak" | "longBreak";

// --- sessionStorage Persistence ---
const SESSION_STORAGE_KEY = "studyTimerState";

interface SavedTimerState {
  status: TimerStatus;
  startTime: number | null;
  accumulatedPausedTime: number;
  pauseStartTime: number | null;
  currentPhase: PhaseType;
  completedSessions: number;
  mode: TimerMode;
  selectedSubject: string;
  saveTimestamp: number; // When the state was saved
  taskName: string; // Add taskName to saved state
  taskType: string; // Add taskType to saved state
}

interface StudyTimerProps {
  mode: TimerMode;
  hideNotificationButton?: boolean;
  onStatusChange?: (status: TimerStatus) => void; // Add callback for status changes
  groupSync?: { // Group sync logic is temporarily disabled during refactor
    groupId: string;
    state: {
      isRunning: boolean;
      startTime: number | null;
      timeRemaining: number; // Needs adaptation for new logic
      currentPhase: PhaseType;
      completedSessions: number;
      mode: TimerMode;
    };
    setState: (state: any) => Promise<void>;
  };
}

interface StudySession {
  userId: string;
  subject: string;
  taskName: string;
  taskType: string; // Add this field to track the type of task (Lecture, Exercise, Reading, etc.)
  startTime: Date; // Timestamp of when this specific session segment started
  endTime: Date;
  duration: number; // Duration in seconds
  mode: TimerMode;
  phase: PhaseType;
  completed: boolean;
  date: string; // YYYY-MM-DD format
  weekNumber: number;
  month: string; // YYYY-MM format
  year: number;
  subjectColor?: string; // Optional color for the subject
  feedback?: string; // User's notes on the session
  productivityRating?: number; // User's 1-5 productivity rating
}

// New interface for pause records
interface PauseRecord {
  userId: string;
  sessionId: string; // References the original session
  subject: string;
  task: string;
  taskType: string; // Add task type
  timestamp: number; // When the pause occurred
  currentDuration: number; // Duration at time of pause
  note?: string; // Optional note about the pause
}

const DEFAULT_SETTINGS: TimerSettings = {
  workDuration: 25 * 60,
  shortBreakDuration: 5 * 60,
  longBreakDuration: 15 * 60,
  sessionsUntilLongBreak: 4,
  notificationInterval: 60, // Default to 60 minutes
  dayResetHour: 4, // Default day reset time to 4 AM
};



// Maximum reasonable time (24 hours in seconds)
const MAX_REASONABLE_TIME = 24 * 60 * 60;

// Validate time to prevent unrealistic values
const validateTime = (seconds: number): number => {
  if (typeof seconds !== 'number' || isNaN(seconds)) return 0;
  if (seconds < 0) return 0;
  return Math.min(seconds, MAX_REASONABLE_TIME);
};

const formatDuration = (seconds: number) => {
  const validSeconds = validateTime(seconds);
  const hours = Math.floor(validSeconds / 3600);
  const minutes = Math.floor((validSeconds % 3600) / 60);
  const secs = Math.round(validSeconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  }
  return `${secs}s`;
};

export function StudyTimer({ mode, hideNotificationButton = false, onStatusChange /*, groupSync */ }: StudyTimerProps) { // groupSync temporarily disabled
  const navigate = useNavigate();

  // --- New Timer State ---
  const [timerStatus, setTimerStatus] = useState<TimerStatus>("idle");
  const [startTime, setStartTime] = useState<number | null>(null); // Timestamp when timer started/resumed (Date.now())
  const [accumulatedPausedTime, setAccumulatedPausedTime] = useState<number>(0); // Total ms spent paused
  const [displayTime, setDisplayTime] = useState<number>(0); // Time shown in UI (seconds), calculated from state
  const timerWorkerRef = useRef<Worker | null>(null);
  const pauseStartTimeRef = useRef<number | null>(null); // Track timestamp when pause began
  const sessionStartTimeRef = useRef<number | null>(null); // Track timestamp when the current *session* (work/stopwatch) started for saving

  // --- Other State ---
  const [taskName, setTaskName] = useState(""); // Keep task name if needed for saving
  const [currentPhase, setCurrentPhase] = useState<PhaseType>("work");
  const [completedSessions, setCompletedSessions] = useState(0);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [lastNotifiedInterval, setLastNotifiedInterval] = useState(0); // State for stopwatch interval notification
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermissionStatus>("default"); // Track permission status

  // New state for task dialog
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [pendingTaskName, setPendingTaskName] = useState("");
  const [startTimerAfterTask, setStartTimerAfterTask] = useState(false);

  // Picture-in-Picture state
  const [isPipSupported, setIsPipSupported] = useState(false);
  const [isPipActive, setIsPipActive] = useState(false);
  const pipVideoRef = useRef<HTMLVideoElement | null>(null);
  const pipCanvasRef = useRef<HTMLCanvasElement | null>(null);

  // Save a reference to the last time we fetched settings from Firebase
  const lastFetchTimeRef = useRef<number>(0);

  // Settings - now prioritizes Firebase with localStorage fallback
  const [settings, setSettings] = useState<TimerSettings>(DEFAULT_SETTINGS);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [tempSettings, setTempSettings] = useState<TimerSettings>(DEFAULT_SETTINGS);
  const [showSettings, setShowSettings] = useState(false);

  const { user } = useSupabaseAuth();

  // Internal state for mode to allow restoration (Moved up)
  const [modeInternal, setModeInternal] = useState(mode);
  useEffect(() => {
    setModeInternal(mode);
    // Reset timer when mode prop changes externally, potentially clear storage?
    // resetTimer(false); // Decide if mode change should reset and clear storage
  }, [mode]);

  // New state for session summary dialog
  const [isSessionSummaryOpen, setIsSessionSummaryOpen] = useState(false);
  const [sessionSummaryType, setSessionSummaryType] = useState<"complete" | "pause">("complete");
  const [sessionDuration, setSessionDuration] = useState(0);
  const [sessionFeedback, setSessionFeedback] = useState("");
  const [sessionProductivity, setSessionProductivity] = useState(3); // 1-5 scale

  // --- New state for task type ---
  const [taskType, setTaskType] = useState<string>("Study"); // Default to "Study"

  // New state for pending task type in dialog
  const [pendingTaskType, setPendingTaskType] = useState("Study");

  // Add state for Spotify bar visibility
  const [spotifyVisible, setSpotifyVisible] = useLocalStorage<boolean>('spotify-is-visible', true);

  // --- sessionStorage Helpers ---
  const saveStateToSessionStorage = useCallback(() => {
    const stateToSave: SavedTimerState = {
      status: timerStatus,
      startTime: startTime,
      accumulatedPausedTime: accumulatedPausedTime,
      pauseStartTime: pauseStartTimeRef.current,
      currentPhase: currentPhase,
      completedSessions: completedSessions,
      mode: mode, // Use original prop 'mode' here for saving consistency
      selectedSubject: selectedSubject?.name || "",
      saveTimestamp: Date.now(),
      taskName: taskName, // Save task name to session storage
      taskType: taskType, // Save task type to session storage
    };
    try {
      sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(stateToSave));
      // console.log("State saved to sessionStorage:", stateToSave);
    } catch (error) {
      console.error("Error saving state to sessionStorage:", error);
    }
  }, [timerStatus, startTime, accumulatedPausedTime, currentPhase, completedSessions, mode, selectedSubject, taskName, taskType]); // Added taskType to dependencies

  const loadStateFromSessionStorage = (): SavedTimerState | null => {
    try {
      const savedStateString = sessionStorage.getItem(SESSION_STORAGE_KEY);
      if (savedStateString) {
        const savedState = JSON.parse(savedStateString) as SavedTimerState;
        // Basic validation
        if (savedState && typeof savedState.status === 'string' && typeof savedState.saveTimestamp === 'number') {
           // console.log("State loaded from sessionStorage:", savedState);
           return savedState;
        }
      }
    } catch (error) {
      console.error("Error loading state from sessionStorage:", error);
    }
    return null;
  };

  const clearStateFromSessionStorage = () => {
    try {
      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      // console.log("State cleared from sessionStorage");
    } catch (error) {
      console.error("Error clearing state from sessionStorage:", error);
    }
  };

  // --- Firebase Saving Logic (Adapted) ---
  const getWeekNumber = (date: Date) => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  };

  // Helper function to get the adjusted date based on reset hour
  const getAdjustedDateForSession = (timestamp: number, resetHour: number): Date => {
    const currentDate = new Date(timestamp);
    const currentHour = currentDate.getHours();

    if (currentHour < resetHour) {
      // If current hour is before reset hour, consider it the previous day
      const previousDay = new Date(timestamp);
      previousDay.setDate(currentDate.getDate() - 1);
      return previousDay;
    }
    // Otherwise, it's the current calendar day
    return currentDate;
  };

  const saveStudySessionToSupabase = async (
    duration: number,
    completed: boolean = true,
    feedback: string = "",
    productivityRating: number = 0
  ) => {
    // Use sessionStartTimeRef for the accurate start time of this specific segment
    if (!user || !sessionStartTimeRef.current || !selectedSubject || duration <= 0) {
        console.warn("Skipping saveStudySession due to missing data or zero duration", { user, sessionStartTime: sessionStartTimeRef.current, selectedSubject, duration });
        return;
    }

    const now = Date.now();
    // Get the date adjusted for the user's defined day reset time
    const adjustedDate = getAdjustedDateForSession(now, settings.dayResetHour);

    // Format date in local time zone (YYYY-MM-DD)
    const formatDateToLocalYYYYMMDD = (date: Date): string => {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // Create Date objects and ensure they're properly formatted
    const startTimeDate = new Date(sessionStartTimeRef.current);
    const formattedStartTime = startTimeDate.toISOString();
    const formattedEndTime = new Date(now).toISOString(); // Use original 'now' for end time

    // Only include fields that exist in the Supabase table
    const sessionData = {
      id: sessionStartTimeRef.current.toString(),
      user_id: user.id,
      subject: selectedSubject.name,
      task_name: taskName || "Study Session",
      task_type: taskType,
      start_time: formattedStartTime,
      end_time: formattedEndTime,
      duration: Math.round(validateTime(duration)), // Duration in seconds, validated
      mode: modeInternal,
      phase: currentPhase,
      completed,
      date: formatDateToLocalYYYYMMDD(adjustedDate),
      notes: feedback || "",
      productivity_rating: productivityRating || 0
    };

    try {
      await saveStudySession(sessionData);
      // console.log("Study session saved:", sessionData);
    } catch (error) {
      console.error("Error saving study session:", error);
      // Avoid toast for background saves, rely on console logs
      // toast({ title: "Save Error", description: "Could not save study session.", variant: "destructive" });
    }
  };

  // --- Pomodoro Phase Completion (Adapted & Moved Up) ---
  const handleTimerComplete = useCallback(() => { // Make useCallback
    if (modeInternal !== "pomodoro" || timerStatus !== 'running') return;

    const audio = new Audio("/notification.mp3");
    audio.play().catch(console.error);

    // Function to show browser notification (uses state now)
    const showCompletionNotification = (title: string, body: string) => {
      if (notificationPermission === 'granted') {
        new Notification(title, { body, icon: '/favicon.ico' });
      } else {
        console.log('Notification permission not granted or denied, skipping notification.');
      }
    };

    let nextPhase: PhaseType;
    let nextDuration: number;
    const newCompletedSessions = completedSessions + 1; // Calculate based on current state

    if (currentPhase === "work") {
      // Save completed work session *before* updating state
      // Duration should be the configured work duration, assuming it ran fully
      saveStudySessionToSupabase(settings.workDuration, true);

      // Determine next phase based on new session count
      let notificationTitle = "Work Complete!";
      let notificationBody = "";
      if (newCompletedSessions % settings.sessionsUntilLongBreak === 0) {
        nextPhase = "longBreak";
        nextDuration = settings.longBreakDuration;
        notificationBody = "Time for a long break! 🎉";
        toast({ title: notificationTitle, description: notificationBody });
      } else {
        nextPhase = "shortBreak";
        nextDuration = settings.shortBreakDuration;
        notificationBody = "Time for a short break! ☕";
        toast({ title: notificationTitle, description: notificationBody });
      }
      // Show browser notification
      showCompletionNotification(notificationTitle, notificationBody);
      // Update completed sessions state *after* determining phase
      setCompletedSessions(newCompletedSessions);

    } else { // Break finished
      nextPhase = "work";
      nextDuration = settings.workDuration;
      const notificationTitle = "Break's Over!";
      const notificationBody = "Back to work! 💪";
      toast({ title: notificationTitle, description: notificationBody });
      // Show browser notification for break completion as well
      showCompletionNotification(notificationTitle, notificationBody);
      // Don't increment completedSessions here
    }

    // Update state for the next phase
    const now = Date.now();
    setCurrentPhase(nextPhase);
    setDisplayTime(nextDuration);
    setStartTime(now);
    setAccumulatedPausedTime(0);
    sessionStartTimeRef.current = now; // Start new session segment
    pauseStartTimeRef.current = null;
    setTimerStatus("running"); // Ensure status is running

    // Save the state for the newly started phase
    saveStateToSessionStorage();

    // Ensure worker ticks continue (the effect depending on timerStatus will handle this)
    // if (timerWorkerRef.current) {
    //   timerWorkerRef.current.postMessage({ type: 'START' });
    // }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modeInternal, timerStatus, currentPhase, completedSessions, settings, saveStudySessionToSupabase, saveStateToSessionStorage]); // Add dependencies


  // --- Core Timer Calculation ---
  const calculateDisplayTime = useCallback(() => {
    // If idle, displayTime is set by reset/settings change, no calculation needed
    if (timerStatus === "idle") {
      return;
    }

    // If paused, calculate based on time up to the pause start
    if (timerStatus === "paused" && startTime !== null && pauseStartTimeRef.current !== null) {
      const elapsedMs = pauseStartTimeRef.current - startTime - accumulatedPausedTime; // Subtract accumulated pause time
      if (modeInternal === "pomodoro") {
        let phaseDurationSecs: number;
        switch (currentPhase) {
          case "work": phaseDurationSecs = settings.workDuration; break;
          case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
          case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
          default: phaseDurationSecs = settings.workDuration;
        }
        const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(elapsedMs / 1000));
        setDisplayTime(remainingSeconds);
      } else { // Stopwatch mode
        setDisplayTime(validateTime(Math.floor(elapsedMs / 1000)));
      }
      return; // Don't proceed to running calculation
    }

    // If running, calculate based on current time
    if (timerStatus === "running" && startTime !== null) {
      const now = Date.now();
      // Active elapsed time = (current time - start time) - total time spent paused
      const activeElapsedMs = now - startTime - accumulatedPausedTime;

      if (modeInternal === "pomodoro") {
        let phaseDurationSecs: number;
        switch (currentPhase) {
          case "work": phaseDurationSecs = settings.workDuration; break;
          case "shortBreak": phaseDurationSecs = settings.shortBreakDuration; break;
          case "longBreak": phaseDurationSecs = settings.longBreakDuration; break;
          default: phaseDurationSecs = settings.workDuration;
        }
        const remainingSeconds = Math.max(0, phaseDurationSecs - Math.floor(activeElapsedMs / 1000));
        setDisplayTime(remainingSeconds);

        if (remainingSeconds <= 0) {
          handleTimerComplete(); // Automatically transition
        }
      } else { // Stopwatch mode
        const elapsedSeconds = Math.floor(activeElapsedMs / 1000);
        const validatedElapsedSeconds = validateTime(elapsedSeconds);
        setDisplayTime(validatedElapsedSeconds); // Validate stopwatch time

        // Stopwatch Custom Notification Interval Logic
        const notificationIntervalSeconds = settings.notificationInterval * 60; // Convert minutes to seconds
        const currentIntervalMark = Math.floor(validatedElapsedSeconds / notificationIntervalSeconds);
        if (notificationIntervalSeconds > 0 && currentIntervalMark > 0 && currentIntervalMark > lastNotifiedInterval) {
          const minutesPassed = currentIntervalMark * settings.notificationInterval;
          console.log(`Stopwatch: ${minutesPassed} minute(s) passed.`);
          // Use state variable for permission check
          if (notificationPermission === 'granted') {
            new Notification('Study Update', {
              body: `${minutesPassed} minute${minutesPassed > 1 ? 's' : ''} passed! Keep going!`,
              icon: '/favicon.ico', // Ensure this path is correct relative to the public folder
              tag: `stopwatch-interval-${currentIntervalMark}` // Use tag to prevent duplicate notifications
            });
          }
          setLastNotifiedInterval(currentIntervalMark); // Update the last notified interval
        }
      }
    }
  }, [timerStatus, startTime, accumulatedPausedTime, modeInternal, currentPhase, settings, handleTimerComplete, lastNotifiedInterval]); // Added lastNotifiedInterval dependency

  // --- Worker Setup and Communication ---
  useEffect(() => {
    // Initialize worker
    if (!timerWorkerRef.current) {
       try {
          timerWorkerRef.current = new Worker('/timerWorker.js');
          console.log("Worker initialized"); // Added log
       } catch (error) {
          console.error('Error initializing worker:', error);
          toast({
            title: "Timer Error",
            description: "Could not initialize the timer worker. Please refresh.",
            variant: "destructive"
          });
          return; // Exit if worker fails to initialize
       }
    }

    const worker = timerWorkerRef.current; // Use the ref

    // Define message handler using the latest calculateDisplayTime
    const handleWorkerMessage = (e: MessageEvent) => {
      timerWorkerRef.current = worker;

      if (e.data.type === 'TICK') {
        // console.log('Main: Received TICK');
        calculateDisplayTime(); // calculateDisplayTime is now a dependency of this effect
      }
    };

    // Define error handler
    const handleWorkerError = (error: ErrorEvent) => {
      console.error('Worker error:', error);
      toast({
        title: "Timer Error",
        description: "An unexpected error occurred with the timer worker.",
        variant: "destructive"
      });
      resetTimer(false); // Reset without saving
    };

    // Assign handlers
    worker.onmessage = handleWorkerMessage;
    worker.onerror = handleWorkerError;

    // If timer status changes to running, ensure worker is started
    // If timer status changes away from running, ensure worker is stopped
    if (timerStatus === 'running') {
        console.log("Effect: Timer is running, ensuring worker ticks START");
        worker.postMessage({ type: 'START' });
    } else {
        console.log("Effect: Timer is NOT running, ensuring worker ticks STOP");
        worker.postMessage({ type: 'STOP' });
    }

    // Cleanup function for this effect iteration
    return () => {
      // Remove the specific handlers assigned in this effect run
      // This prevents issues if the component re-renders quickly
      if (worker) {
          worker.onmessage = null;
          worker.onerror = null;
      }
      // Note: We don't terminate the worker here, only in the final unmount cleanup
    };
  }, [calculateDisplayTime, timerStatus]); // Re-run when calculation logic or status changes

  // Separate effect for final worker termination on unmount
  useEffect(() => {
    // Add beforeunload listener to save state as a fallback
    const handleBeforeUnload = () => {
        // Only save if timer is active (running or paused)
        if (timerStatus === 'running' || timerStatus === 'paused') {
            saveStateToSessionStorage();
        }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        // Save state one last time on unmount if active
        if (timerStatus === 'running' || timerStatus === 'paused') {
             saveStateToSessionStorage();
        }
        // Terminate worker
        if (timerWorkerRef.current) {
            console.log('Main: Terminating worker on unmount');
            timerWorkerRef.current.postMessage({ type: 'STOP' });
            timerWorkerRef.current.terminate();
            timerWorkerRef.current = null;
        }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timerStatus, saveStateToSessionStorage]); // Include timerStatus and save function


  // --- useEffect for initialization ---
  useEffect(() => {
    // Wait until settings have loaded before initializing the timer
    if (isLoadingSettings) {
      return;
    }

    // Check for notifications support and set based on stored permission
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission as NotificationPermissionStatus);
    }

    // Attempt to restore saved state from sessionStorage
    const savedState = loadStateFromSessionStorage();
    if (savedState) {
      // Don't restore if too old (24 hours)
      const MAX_AGE_MS = 24 * 60 * 60 * 1000; // 24 hours
      const now = Date.now();
      const stateAge = now - savedState.saveTimestamp;

      if (stateAge > MAX_AGE_MS) {
        console.log(`Saved state is too old (${Math.round(stateAge / 3600000)} hours), not restoring`);
        clearStateFromSessionStorage();
        return;
      }

      try {
        const status = savedState.status;
        setTimerStatus(status);

        // Restore subject if name exists
        if (savedState.selectedSubject) {
          getExtendedUserProfile(user?.id || "").then((userData) => {
            const subjects = userData?.subjects || [];
            const subject = subjects.find((s: any) => s.name === savedState.selectedSubject);
            if (subject) {
              setSelectedSubject(subject);
            }
          }).catch(console.error);
        }

        // Common state to restore regardless of status
        setCurrentPhase(savedState.currentPhase);
        setCompletedSessions(savedState.completedSessions);
        setModeInternal(savedState.mode);

        // Restore task name and type if they exist
        if (savedState.taskName) {
          setTaskName(savedState.taskName);
        }

        if (savedState.taskType) {
          setTaskType(savedState.taskType);
          setPendingTaskType(savedState.taskType); // Also set the pending task type for dialog
        }

        if (status === "running" || status === "paused") {
          if (savedState.startTime !== null) {
            setStartTime(savedState.startTime);
            setAccumulatedPausedTime(savedState.accumulatedPausedTime || 0);

            if (status === "paused" && savedState.pauseStartTime !== null) {
              pauseStartTimeRef.current = savedState.pauseStartTime;
            }

            // Restore sessionStartTimeRef if we have a valid startTime
            if (savedState.startTime > 0) {
              sessionStartTimeRef.current = savedState.startTime;
            }

            // Force a display time calculation now
            // This will be recalculated when the worker starts ticking
            calculateDisplayTime();
          } else {
            // Invalid state, reset
            console.warn("Saved status was running/paused but startTime was null, resetting");
            resetTimer(false);
          }
        } else { // idle
          // Set appropriate display time based on mode and phase
          if (savedState.mode === "pomodoro") {
            switch (savedState.currentPhase) {
              case "work": setDisplayTime(settings.workDuration); break;
              case "shortBreak": setDisplayTime(settings.shortBreakDuration); break;
              case "longBreak": setDisplayTime(settings.longBreakDuration); break;
              default: setDisplayTime(settings.workDuration);
            }
          } else { // stopwatch
            setDisplayTime(0);
          }
        }
      } catch (error) {
        console.error("Error restoring state:", error);
        resetTimer(false);
      }
    } else {
      // Set initial display time based on mode
      if (modeInternal === "pomodoro") {
        setDisplayTime(settings.workDuration);
      } else {
        setDisplayTime(0);
      }
    }

    // Check for PiP support in the browser
    const video = document.createElement('video');
    let isWebkitSupported = false;
    let isStandardSupported = false;

    // Check for webkit (Safari) PiP support
    if (video.webkitSupportsPresentationMode && typeof video.webkitSetPresentationMode === 'function') {
      isWebkitSupported = true;
    }

    // Check for standard PiP support
    if (document.pictureInPictureEnabled || ('pictureInPictureEnabled' in document)) {
      isStandardSupported = true;
    }

    setIsPipSupported(isWebkitSupported || isStandardSupported);

    // Add beforeunload listener to save state when user leaves/refreshes
    const handleUnload = () => {
      if (timerStatus !== 'idle') {
        saveStateToSessionStorage();
      }
    };
    window.addEventListener('beforeunload', handleUnload);

    // Cleanup function
    return () => {
      // Save state on unmount for potential recovery
      saveStateToSessionStorage();

      // Terminate worker on final unmount
      if (timerWorkerRef.current) {
        timerWorkerRef.current.terminate();
        timerWorkerRef.current = null;
      }

      // Remove beforeunload listener
      window.removeEventListener('beforeunload', handleUnload);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoadingSettings]); // Add isLoadingSettings as a dependency


  // Effect to save state when selected subject changes
  useEffect(() => {
    if (timerStatus !== 'idle') { // Only save if timer has been interacted with
        saveStateToSessionStorage();
    }
  }, [selectedSubject, saveStateToSessionStorage, timerStatus]);

  // Effect to notify parent component of timer status changes
  useEffect(() => {
    if (onStatusChange) {
      onStatusChange(timerStatus);
    }
  }, [timerStatus, onStatusChange]);


  // Effect to set initial display time when settings change (if idle)
  useEffect(() => {
    if (timerStatus === 'idle') {
      if (modeInternal === 'pomodoro') {
        setDisplayTime(settings.workDuration);
        // Don't reset phase/sessions here, only on explicit reset or mode change
      } else {
        setDisplayTime(0);
      }
    }
  }, [settings, timerStatus, modeInternal]);


  // --- Picture-in-Picture Logic (Largely Unchanged, uses displayTime) ---

  const updatePipCanvas = useCallback(() => {
    const canvas = pipCanvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d', { alpha: false });
    if (!ctx) return;

    ctx.fillStyle = '#1a1f3c';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 48px monospace';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const timeToDisplay = validateTime(displayTime); // Use the unified displayTime state
    const hours = Math.floor(timeToDisplay / 3600);
    const minutes = Math.floor((timeToDisplay % 3600) / 60);
    const seconds = Math.floor(timeToDisplay % 60);
    let timeText = '';
    if (hours > 0) {
      timeText = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      timeText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    ctx.fillText(timeText, canvas.width / 2, canvas.height / 2);

    ctx.font = '16px sans-serif';
    ctx.fillText(selectedSubject?.name || 'No Subject', canvas.width / 2, 30);
    if (mode === "pomodoro") { // Use original prop 'mode' here for consistency? Or modeInternal? Check usage.
      ctx.fillStyle = '#a855f7';
      ctx.font = '14px sans-serif';
      ctx.fillText(currentPhase.charAt(0).toUpperCase() + currentPhase.slice(1), canvas.width / 2, canvas.height - 30);
    }
  }, [mode, displayTime, selectedSubject, currentPhase]); // Depend on displayTime

  useEffect(() => { // Setup PiP elements
    if (!isPipSupported) return;
    let stream: MediaStream | null = null;
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 320; canvas.height = 180;
      pipCanvasRef.current = canvas;
      const video = document.createElement('video');
      video.muted = true; video.playsInline = true; video.autoplay = true; video.loop = true;
      stream = canvas.captureStream ? canvas.captureStream(30) : (canvas as any).webkitCaptureStream ? (canvas as any).webkitCaptureStream() : null; // Added Safari fallback
      if (!stream) throw new Error("Canvas captureStream not supported");
      video.srcObject = stream;
      video.addEventListener('error', (e) => console.error('Video error:', e));
      video.addEventListener('enterpictureinpicture', () => { setIsPipActive(true); updatePipCanvas(); });
      video.addEventListener('leavepictureinpicture', () => setIsPipActive(false));
      video.play().catch((error) => { console.error('Video play error:', error); video.muted = true; return video.play(); });
      pipVideoRef.current = video;
      video.style.position = 'fixed'; video.style.opacity = '0'; video.style.pointerEvents = 'none'; video.style.zIndex = '-1';
      document.body.appendChild(video);
      updatePipCanvas();
      return () => {
        try {
          if (document.pictureInPictureElement === video) document.exitPictureInPicture().catch(console.error);
          video.srcObject = null;
          if (document.body.contains(video)) document.body.removeChild(video); // Check if attached before removing
          if (stream) stream.getTracks().forEach(track => track.stop());
          pipVideoRef.current = null; pipCanvasRef.current = null;
        } catch (error) { console.error('PiP Cleanup error:', error); }
      };
    } catch (error) {
      console.error('Error setting up PiP elements:', error); setIsPipSupported(false);
      if (stream) stream.getTracks().forEach(track => track.stop());
      return () => {};
    }
  }, [isPipSupported]); // Removed updatePipCanvas dependency

  useEffect(() => { // Update PiP canvas content
    if (!isPipActive || !pipCanvasRef.current || !pipVideoRef.current) return;
    if (pipVideoRef.current.paused) pipVideoRef.current.play().catch(console.error);
    updatePipCanvas();
  }, [isPipActive, displayTime, selectedSubject, currentPhase, updatePipCanvas]); // Depend on displayTime

  const togglePictureInPicture = async () => { // Toggle PiP
    try {
      if (!pipVideoRef.current) return;
      if (document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      } else {
        if (pipVideoRef.current.paused) await pipVideoRef.current.play();
        await pipVideoRef.current.requestPictureInPicture();
        updatePipCanvas();
      }
    } catch (error) {
      console.error('Error toggling PiP:', error);
      toast({ title: "PiP Error", description: `Could not toggle PiP: ${error instanceof Error ? error.message : 'Unknown error'}`, variant: "destructive" });
    }
  };

  useEffect(() => { // Check PiP Support
    const checkPipSupport = () => {
      try {
        const standardPipSupported = document.pictureInPictureEnabled;
        // @ts-ignore - Safari support
        const safariPipSupported = document.webkitPictureInPictureEnabled;
        const testVideo = document.createElement('video');
        // @ts-ignore - Safari specific API
        const safariPresentationSupported = testVideo.webkitSupportsPresentationMode && typeof testVideo.webkitSetPresentationMode === 'function';
        setIsPipSupported(!!(standardPipSupported || safariPipSupported || safariPresentationSupported));
      } catch (error) { console.error('Error checking PiP support:', error); setIsPipSupported(false); }
    };
    checkPipSupport();
  }, []);

  // Handle subject change
  const handleSubjectChange = (subject: Subject | null) => {
    setSelectedSubject(subject);
    // Save state when subject changes
    if (timerStatus !== 'idle') {
      saveStateToSessionStorage();
    }
  };


  // --- Notification Permission Request ---
  const requestNotificationPermission = async (): Promise<NotificationPermissionStatus> => {
    if (!('Notification' in window)) {
      toast({ title: "Notifications Not Supported", description: "Your browser does not support desktop notifications.", variant: "destructive" });
      setNotificationPermission("denied");
      return "denied";
    }

    // Return current status if already decided
    if (notificationPermission === 'granted' || notificationPermission === 'denied') {
      return notificationPermission;
    }

    // Request permission
    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission); // Update state
      if (permission === 'denied') {
        // Use 'default' variant or remove variant prop if 'default' is standard
        toast({ title: "Notifications Disabled", description: "You have denied notification permissions. Please enable them in browser settings if you want timer alerts.", variant: "default", duration: 5000 });
      } else if (permission === 'granted') {
         // Use 'default' variant or remove variant prop if 'default' is standard
         toast({ title: "Notifications Enabled", description: "You will now receive timer alerts.", variant: "default" });
      }
      return permission;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      toast({ title: "Permission Error", description: "Could not request notification permission.", variant: "destructive" });
      setNotificationPermission("denied"); // Assume denied on error
      return "denied";
    }
  };


  // --- Timer Control Functions (Rewritten) ---

  // Modified to open task dialog first
  const startTimer = async () => { // Make async
    if (!selectedSubject || !selectedSubject.name) {
      toast({ title: "Select a subject", description: "Please select a subject first.", variant: "destructive" });
      return;
    }

    // Open task dialog if timer is idle
    if (timerStatus === "idle") {
      setIsTaskDialogOpen(true);
      setPendingTaskName("");
      setStartTimerAfterTask(true);
      return;
    }

    // For resuming from pause, don't ask for task again
    if (timerStatus === "paused") {
      continueTimer();
    }
  };

  // New function to actually start the timer after task is entered
  const continueTimer = async () => {
    // Request permission if it's 'default' *before* proceeding
    if (notificationPermission === 'default') {
      const permissionResult = await requestNotificationPermission();
    }

    const now = Date.now();

    if (timerStatus === "idle") {
      // Starting fresh
      setStartTime(now);
      setAccumulatedPausedTime(0); // Reset accumulated pause time
      pauseStartTimeRef.current = null; // Ensure pause ref is clear
      sessionStartTimeRef.current = now; // Mark the beginning of this session segment

      if (modeInternal === 'pomodoro') {
         setDisplayTime(settings.workDuration); // Set initial display
         setCurrentPhase('work');
         setCompletedSessions(0);
      } else {
         setDisplayTime(0); // Stopwatch starts at 0
      }
      setTimerStatus("running");

    } else if (timerStatus === "paused") {
      // Resuming from pause
      if (pauseStartTimeRef.current !== null) {
        const pauseDurationMs = now - pauseStartTimeRef.current;
        setAccumulatedPausedTime(prev => prev + pauseDurationMs); // Add this pause duration
      }
      pauseStartTimeRef.current = null; // Clear pause start time
      // DO NOT reset startTime - keep the original session start time
      setTimerStatus("running");
    }

    // Save state *after* updating status and potentially permission state
    saveStateToSessionStorage();

    if (timerWorkerRef.current) {
      timerWorkerRef.current.postMessage({ type: 'START' });
    }
  };

  // Handle task dialog confirm
  const handleTaskConfirm = () => {
    setTaskName(pendingTaskName);
    setTaskType(pendingTaskType);
    setIsTaskDialogOpen(false);

    if (startTimerAfterTask) {
      setStartTimerAfterTask(false);
      continueTimer();
    }
  };

  const pauseTimer = () => {
    if (timerStatus !== "running") return;

    if (timerWorkerRef.current) {
      timerWorkerRef.current.postMessage({ type: 'STOP' });
    }

    setTimerStatus("paused");
    const pauseTime = Date.now();
    pauseStartTimeRef.current = pauseTime; // Record when pause began

    // Calculate active duration up to the point of pause
    if (startTime && sessionStartTimeRef.current && (modeInternal === 'stopwatch' || currentPhase === "work")) {
        // Active elapsed = (pause time - session start time) - accumulated pauses *before* this one
        const activeElapsedMs = pauseTime - startTime - accumulatedPausedTime;
        if (activeElapsedMs > 0) { // Only save if there's actual duration
          // Show pause dialog
          setSessionDuration(activeElapsedMs / 1000);
          setSessionFeedback("");
          setSessionProductivity(3);
          setSessionSummaryType("pause");
          setIsSessionSummaryOpen(true);
        }
    }

    // Save state for potential restoration
    saveStateToSessionStorage();
  };

  const resetTimer = async (shouldSave: boolean = true) => {
    if (timerWorkerRef.current) {
      timerWorkerRef.current.postMessage({ type: 'STOP' });
    }

    // Save final session state if running/paused and shouldSave is true
    if (shouldSave && timerStatus !== "idle" && sessionStartTimeRef.current && startTime) {
      let finalActiveDurationSecs = 0;
      const now = Date.now();

      if (timerStatus === 'running') {
        // Active duration = (now - start) - accumulated pauses
        finalActiveDurationSecs = (now - startTime - accumulatedPausedTime) / 1000;
      } else if (timerStatus === 'paused' && pauseStartTimeRef.current) {
        // Active duration = (pause start time - start time) - accumulated pauses
        finalActiveDurationSecs = (pauseStartTimeRef.current - startTime - accumulatedPausedTime) / 1000;
      }

      if (finalActiveDurationSecs > 0) {
        // Show session summary dialog
        setSessionDuration(finalActiveDurationSecs);
        setSessionFeedback("");
        setSessionProductivity(3);
        setSessionSummaryType("complete");
        setIsSessionSummaryOpen(true);
        return; // Don't reset timer until user confirms
      }
    }

    // Reset state if no dialog needs to be shown
    completeReset();
  };

  // New function to actually reset the timer after summary dialog is closed
  const completeReset = () => {
    setTimerStatus("idle"); // Set status first
    setStartTime(null);
    setAccumulatedPausedTime(0); // Reset accumulated time
    pauseStartTimeRef.current = null; // Clear pause start ref
    sessionStartTimeRef.current = null; // Clear session start ref
    setCurrentPhase("work"); // Reset phase
    setCompletedSessions(0); // Reset sessions
    setLastNotifiedInterval(0); // Reset notified interval on timer reset
    setTaskName(""); // Clear task name

    // Reset display time based on mode
    if (modeInternal === "pomodoro") {
      setDisplayTime(settings.workDuration);
    } else {
      setDisplayTime(0);
    }

    // Clear persisted state
    clearStateFromSessionStorage();
  };

  // Handle session summary dialog submission
  const handleSessionSummarySubmit = async () => {
    try {
      if (sessionDuration > 0) {
        if (sessionSummaryType === "complete") {
          // For complete session, save with full data and finish
          await saveStudySessionToSupabase(
            sessionDuration,
            true, // mark as completed
            sessionFeedback,
            sessionProductivity
          );
          toast({
            title: "Session completed!",
            description: `You studied ${selectedSubject?.name} for ${formatDuration(sessionDuration)}`
          });
        } else {
          // For pause, just log the pause event with minimal data
          await savePauseRecord(sessionDuration, sessionFeedback);
          toast({
            title: "Session paused",
            description: `Current duration: ${formatDuration(sessionDuration)}`
          });
        }
      }
    } catch (error) {
      console.error("Error saving session summary:", error);
    }

    // Close dialog
    setIsSessionSummaryOpen(false);

    // If this was a complete action, reset the timer
    if (sessionSummaryType === "complete") {
      completeReset();
    }
  };

  // New function to save a pause record instead of a full session
  const savePauseRecord = async (duration: number, pauseNote: string = "") => {
    if (!user || !sessionStartTimeRef.current || !selectedSubject) {
      return;
    }

    const now = Date.now();
    const pauseId = `pause_${now}`;

    try {
      // For now, we'll save pause records as study sessions with a special flag
      // This can be improved later with a dedicated pause_records table
      await saveStudySession({
        id: pauseId,
        user_id: user.id,
        subject: selectedSubject.name,
        task_name: taskName || "Study Session",
        task_type: taskType,
        start_time: new Date(sessionStartTimeRef.current).toISOString(),
        end_time: new Date(now).toISOString(),
        duration: Math.round(duration),
        mode: modeInternal,
        phase: 'pause', // Special phase for pause records
        completed: false,
        date: new Date().toISOString().split('T')[0],
        notes: pauseNote,
        productivity_rating: 0
      });

    } catch (error) {
      console.error("Error saving pause record:", error);
    }
  };

  // Create separate dialogs for pause and complete
  const renderSessionDialog = () => {
    if (sessionSummaryType === "complete") {
      return (
        <Dialog open={isSessionSummaryOpen} onOpenChange={(open) => {
          // Don't allow closing by clicking outside for completion dialog
          if (!open) {
            return;
          }
          setIsSessionSummaryOpen(open);
        }}>
          <DialogContent className="bg-gradient-to-b from-background to-background/90 dark:from-[#1a1f3c] dark:to-[#131633]/95 backdrop-blur-md border-0 shadow-xl rounded-xl overflow-hidden text-foreground dark:text-white max-w-md">
            <div className="absolute inset-0 bg-purple-500/5 dark:bg-purple-500/10 z-0"></div>
            <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-purple-400 to-indigo-500"></div>
            <div className="relative z-10">
              <DialogHeader className="pb-2">
                <div className="mb-2 flex justify-center">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center bg-green-100 dark:bg-green-900/30 mb-2 animate-bounce-gentle">
                    <span className="text-3xl">🎉</span>
                  </div>
                </div>
                <DialogTitle className="text-2xl font-bold text-center">
                  Session Completed!
                </DialogTitle>
                <DialogDescription className="text-center text-foreground/70 dark:text-white/60">
                  You've studied {selectedSubject?.name || "your subject"}
                  {taskName ? ` on "${taskName}"` : ""}
                  {taskType ? ` (${taskType})` : ""}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div className="flex items-center justify-between bg-gradient-to-r from-purple-100/10 to-indigo-100/10 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-xl p-5 border border-purple-200/20 shadow-sm">
                  <Label htmlFor="sessionDuration" className="text-md font-medium flex items-center gap-2">
                    <span className="p-1.5 rounded-full bg-purple-100/20 dark:bg-purple-900/30">⏱️</span>
                    <span>Total Duration</span>
                  </Label>
                  <div className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-400">{formatDuration(sessionDuration)}</div>
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="sessionProductivity" className="text-md font-medium flex items-center gap-2">
                    <span className="p-1.5 rounded-full bg-purple-100/20 dark:bg-purple-900/30">✨</span>
                    <span>How productive was this session?</span>
                  </Label>
                  <div className="flex justify-between items-center gap-2 mt-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => setSessionProductivity(rating)}
                        className={`flex flex-col items-center p-3 rounded-xl transition-all duration-200 ${
                          sessionProductivity === rating
                            ? "bg-gradient-to-br from-purple-600 to-indigo-600 text-white scale-105 shadow-md"
                            : "bg-gradient-to-br from-background/80 to-background/30 dark:from-white/5 dark:to-white/2 text-muted-foreground hover:from-purple-100/20 hover:to-indigo-100/20 dark:hover:from-purple-900/20 dark:hover:to-indigo-900/20 border border-purple-100/10 dark:border-white/5"
                        }`}
                      >
                        <span className="text-2xl mb-1">
                          {rating === 1 ? "😔" :
                           rating === 2 ? "😐" :
                           rating === 3 ? "🙂" :
                           rating === 4 ? "😊" : "🤩"}
                        </span>
                        <span className="text-xs">
                          {rating === 1 ? "Poor" :
                           rating === 2 ? "Fair" :
                           rating === 3 ? "Good" :
                           rating === 4 ? "Great" : "Excellent!"}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="sessionFeedback" className="text-md font-medium flex items-center gap-2">
                    <span className="p-1.5 rounded-full bg-purple-100/20 dark:bg-purple-900/30">📝</span>
                    <span>Session summary (optional)</span>
                  </Label>
                  <textarea
                    id="sessionFeedback"
                    value={sessionFeedback}
                    onChange={(e) => setSessionFeedback(e.target.value)}
                    placeholder="What did you accomplish? What did you learn?"
                    rows={3}
                    className="resize-none px-4 py-3 bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-purple-100/20 dark:border-white/10 rounded-xl text-foreground dark:text-white shadow-sm transition-all duration-200 focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400"
                  />
                </div>
              </div>
              <DialogFooter className="flex-col sm:flex-row gap-3 pt-3">
                <Button
                  onClick={handleSessionSummarySubmit}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md hover:shadow-lg transition-all duration-200 rounded-xl px-6 py-2.5 text-base font-medium"
                >
                  Save & Finish
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>
      );
    } else {
      // Pause dialog
      return (
        <Dialog open={isSessionSummaryOpen} onOpenChange={setIsSessionSummaryOpen}>
          <DialogContent className="bg-gradient-to-b from-background to-background/90 dark:from-[#1a1f3c] dark:to-[#131633]/95 backdrop-blur-md border-0 shadow-xl rounded-xl overflow-hidden text-foreground dark:text-white max-w-md">
            <div className="absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 z-0"></div>
            <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-amber-400 to-yellow-500"></div>
            <div className="relative z-10">
              <DialogHeader className="pb-2">
                <div className="mb-2 flex justify-center">
                  <div className="w-14 h-14 rounded-full flex items-center justify-center bg-amber-100 dark:bg-amber-900/30 mb-2">
                    <span className="text-2xl">⏸️</span>
                  </div>
                </div>
                <DialogTitle className="text-2xl font-bold text-center">
                  Session Paused
                </DialogTitle>
                <DialogDescription className="text-center text-foreground/70 dark:text-white/60">
                  You're taking a break from {selectedSubject?.name || "studying"}
                  {taskType ? ` (${taskType})` : ""}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-5 py-4">
                <div className="flex items-center justify-between bg-gradient-to-r from-amber-100/10 to-yellow-100/10 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl p-5 border border-amber-200/20 shadow-sm">
                  <Label htmlFor="sessionDuration" className="text-md font-medium flex items-center gap-2">
                    <span className="p-1.5 rounded-full bg-amber-100/20 dark:bg-amber-900/30">⏱️</span>
                    <span>Current Duration</span>
                  </Label>
                  <div className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-yellow-400">{formatDuration(sessionDuration)}</div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="sessionFeedback" className="text-md font-medium flex items-center gap-2">
                    <span className="p-1.5 rounded-full bg-amber-100/20 dark:bg-amber-900/30">📝</span>
                    <span>Quick note (optional)</span>
                  </Label>
                  <textarea
                    id="sessionFeedback"
                    value={sessionFeedback}
                    onChange={(e) => setSessionFeedback(e.target.value)}
                    placeholder="Why are you pausing? What's next?"
                    rows={2}
                    className="resize-none px-4 py-3 bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-amber-100/20 dark:border-white/10 rounded-xl text-foreground dark:text-white shadow-sm transition-all duration-200 focus:ring-2 focus:ring-amber-500/30 focus:border-amber-400"
                  />
                </div>
              </div>
              <DialogFooter className="flex-col sm:flex-row sm:justify-between gap-3 pt-3">
                <Button
                  variant="outline"
                  onClick={() => setIsSessionSummaryOpen(false)}
                  className="w-full sm:w-auto bg-transparent hover:bg-background/80 dark:hover:bg-white/5 text-foreground dark:text-white/80 border border-amber-100/20 dark:border-white/10 hover:text-foreground hover:border-amber-300/30 transition-all duration-200 rounded-lg px-5 py-2"
                >
                  Skip
                </Button>
                <Button
                  onClick={handleSessionSummarySubmit}
                  className="w-full sm:w-auto bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-200 rounded-lg px-6 py-2"
                >
                  Save & Continue
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>
      );
    }
  };

  // --- Settings Handling (Unchanged logic, but ensure save) ---
  const navigateToSettings = () => {
    navigate("/settings");
  };

  // --- UI Rendering (Adapted for new state) ---
  const getPhaseEmoji = () => {
    switch (currentPhase) {
      case "work": return "📖";
      case "shortBreak": return "☕";
      case "longBreak": return "🌟";
      default: return "⏱️";
    }
  };

  // Check if PiP is supported by the browser
  const checkPipSupport = () => {
    const video = document.createElement('video');
    let isWebkitSupported = false;
    let isStandardSupported = false;

    // Check for webkit (Safari) PiP support
    if (video.webkitSupportsPresentationMode && typeof video.webkitSetPresentationMode === 'function') {
      isWebkitSupported = true;
    }

    // Check for standard PiP support
    if (document.pictureInPictureEnabled || ('pictureInPictureEnabled' in document)) {
      isStandardSupported = true;
    }

    setIsPipSupported(isWebkitSupported || isStandardSupported);
  };

  // Add beforeunload listener to save state as a fallback
  const handleBeforeUnload = () => {
    if (timerStatus !== 'idle') {
      saveStateToSessionStorage();
    }
  };

  // Add beforeunload listener
  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [timerStatus, saveStateToSessionStorage]);

  // Removed workerError display block as error handling is now via toast/console

  // New function to handle completing a session early
  const completeSession = async () => {
    if (timerStatus !== 'idle' && sessionStartTimeRef.current && startTime) {
      // Stop the timer worker
      if (timerWorkerRef.current) {
        timerWorkerRef.current.postMessage({ type: 'STOP' });
      }

      // Calculate final duration
      let finalActiveDurationSecs = 0;
      const now = Date.now();

      if (timerStatus === 'running') {
        // Active duration = (now - start) - accumulated pauses
        finalActiveDurationSecs = (now - startTime - accumulatedPausedTime) / 1000;
      } else if (timerStatus === 'paused' && pauseStartTimeRef.current) {
        // Active duration = (pause start time - start time) - accumulated pauses
        finalActiveDurationSecs = (pauseStartTimeRef.current - startTime - accumulatedPausedTime) / 1000;
      }

      if (finalActiveDurationSecs > 0) {
        // Show complete session dialog
        setSessionDuration(finalActiveDurationSecs);
        setSessionFeedback("");
        setSessionProductivity(3);
        setSessionSummaryType("complete");
        setIsSessionSummaryOpen(true);
      }
    }
  };

  // Load settings from Firebase or localStorage
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) {
        // No user, use localStorage
        loadSettingsFromLocalStorage();
        setIsLoadingSettings(false);
        return;
      }

      try {
        // Check if we should fetch from Firebase based on time elapsed
        // Only fetch if it's been more than 5 minutes since last fetch
        const now = Date.now();
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in ms

        if (now - lastFetchTimeRef.current > CACHE_DURATION) {
          // Fetch from Supabase
          const userData = await getExtendedUserProfile(user.id);

          if (userData && userData.stats && userData.stats.timerSettings) {
            // Use settings from Supabase
            const userTimerSettings = {
              workDuration: userData.stats.timerSettings.workDuration ?? DEFAULT_SETTINGS.workDuration,
              shortBreakDuration: userData.stats.timerSettings.shortBreakDuration ?? DEFAULT_SETTINGS.shortBreakDuration,
              longBreakDuration: userData.stats.timerSettings.longBreakDuration ?? DEFAULT_SETTINGS.longBreakDuration,
              sessionsUntilLongBreak: userData.stats.timerSettings.sessionsUntilLongBreak ?? DEFAULT_SETTINGS.sessionsUntilLongBreak,
              notificationInterval: userData.stats.timerSettings.notificationInterval ?? DEFAULT_SETTINGS.notificationInterval,
              dayResetHour: userData.stats.timerSettings.dayResetHour ?? DEFAULT_SETTINGS.dayResetHour
            };

            setSettings(userTimerSettings);

            // Also update localStorage for compatibility
            localStorage.setItem('timerSettings', JSON.stringify(userTimerSettings));

            // Update last fetch time
            lastFetchTimeRef.current = now;
          } else {
            // No settings in Supabase, fall back to localStorage
            loadSettingsFromLocalStorage();
          }
        } else {
          // Use cached settings from localStorage
          loadSettingsFromLocalStorage();
        }
      } catch (error) {
        console.error('Error loading timer settings from Supabase:', error);
        // Fall back to localStorage on error
        loadSettingsFromLocalStorage();
      }

      setIsLoadingSettings(false);
    };

    // Helper function to load settings from localStorage
    const loadSettingsFromLocalStorage = () => {
      try {
        const storedSettings = localStorage.getItem('timerSettings');
        if (storedSettings) {
          const parsedSettings = JSON.parse(storedSettings);
          setSettings(parsedSettings);
        } else {
          // No settings in localStorage, use defaults
          setSettings(DEFAULT_SETTINGS);
        }
      } catch (error) {
        console.error('Error loading timer settings from localStorage:', error);
        // Use defaults if parsing fails
        setSettings(DEFAULT_SETTINGS);
      }
    };

    loadSettings();
  }, [user]);

  // Update settings dialog to also save to Supabase
  const handleSettingsSave = async () => {
    if (!tempSettings) return;

    // Update state and localStorage
    setSettings(tempSettings);
    localStorage.setItem('timerSettings', JSON.stringify(tempSettings));

    // Save to Supabase if user is logged in
    if (user) {
      try {
        // Get current user data to preserve other stats
        const userData = await getExtendedUserProfile(user.id);
        const currentStats = userData?.stats || {};

        await updateUserProfile(user.id, {
          stats: {
            ...currentStats,
            timerSettings: tempSettings,
          },
        });

        // Update last fetch time
        lastFetchTimeRef.current = Date.now();
      } catch (error) {
        console.error('Error saving timer settings to Supabase:', error);
        // Continue even if Supabase save fails, as we've already updated localStorage
      }
    }

    setShowSettings(false);
  };

  // Add effect to update tempSettings when settings change
  useEffect(() => {
    setTempSettings(settings);
  }, [settings]);

  return (
    <div className="flex flex-col items-center gap-10 w-full max-w-xl">
      {/* Loading state for settings */}
      {isLoadingSettings && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
        </div>
      )}

      {/* Subject Selection and Settings UI */}
      <div className="w-full flex justify-center">
        <div className="flex flex-col items-center gap-4">
          {/* Main controls row that becomes column on mobile */}
          <div className="flex flex-col sm:flex-row items-center gap-4">
            <div className="flex items-center w-fit">
              <div className="flex-1">
                <SubjectManager
                  selectedSubject={selectedSubject}
                  onSubjectChange={handleSubjectChange}
                />
              </div>
            </div>
            {/* Controls that move below on mobile */}
            <div className="flex items-center gap-2 mt-2 sm:mt-0">

            {/* Task Input Dialog */}
            <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
              <DialogContent className="bg-gradient-to-b from-background to-background/90 dark:from-[#1a1f3c] dark:to-[#131633]/95 backdrop-blur-md border-0 shadow-xl rounded-xl overflow-hidden text-foreground dark:text-white">
                <div className="absolute inset-0 bg-purple-500/5 dark:bg-purple-500/10 z-0"></div>
                <div className="absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-purple-400 to-indigo-500"></div>
                <div className="relative z-10">
                  <DialogHeader className="pb-2">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
                        <span className="text-xl">📝</span>
                      </div>
                      <DialogTitle className="text-xl font-bold">What are you working on?</DialogTitle>
                    </div>
                    <DialogDescription className="text-foreground/70 dark:text-white/60">
                      Enter a description for your study session to help you stay focused.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    {/* Task Type Selection */}
                    <div className="grid gap-2">
                      <Label htmlFor="taskType" className="text-sm font-medium flex items-center gap-2">
                        Task Type
                      </Label>
                      <Select value={pendingTaskType} onValueChange={setPendingTaskType}>
                        <SelectTrigger className="bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-purple-100/20 dark:border-white/10 text-foreground dark:text-white rounded-lg shadow-sm">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent className="bg-background dark:bg-[#1a1f3c] border-purple-100/20 dark:border-white/10 text-foreground dark:text-white">
                          <SelectItem value="Lecture">Lecture</SelectItem>
                          <SelectItem value="Exercise">Exercise</SelectItem>
                          <SelectItem value="Reading">Reading</SelectItem>
                          <SelectItem value="Practice">Practice</SelectItem>
                          <SelectItem value="Review">Review</SelectItem>
                          <SelectItem value="Study">General Study</SelectItem>
                          <SelectItem value="Custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="taskName" className="text-sm font-medium flex items-center gap-2">
                        Task description
                      </Label>
                      <div className="relative">
                        <Input
                          id="taskName"
                          value={pendingTaskName}
                          onChange={(e) => setPendingTaskName(e.target.value)}
                          placeholder={pendingTaskType === 'Lecture' ? 'Watch lecture on algorithms' :
                                       pendingTaskType === 'Reading' ? 'Read chapter 5' :
                                       pendingTaskType === 'Exercise' ? 'Complete exercise set 3' :
                                       pendingTaskType === 'Practice' ? 'Practice problem solving' :
                                       pendingTaskType === 'Review' ? 'Review for exam' :
                                       'Your study task'}
                          className="bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-purple-100/20 dark:border-white/10 text-foreground dark:text-white pl-4 pr-10 py-6 rounded-lg shadow-sm transition-all duration-200 focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400"
                          autoFocus={false}
                          autoComplete="off"
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter className="mt-2 gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setIsTaskDialogOpen(false)}
                      className="bg-transparent hover:bg-background/80 dark:hover:bg-white/5 text-foreground dark:text-white/80 border border-purple-100/20 dark:border-white/10 hover:text-foreground hover:border-purple-300/30 transition-all duration-200 rounded-lg px-4 py-2"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleTaskConfirm}
                      className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0 rounded-lg px-6 py-2"
                    >
                      Start Session
                    </Button>
                  </DialogFooter>
                </div>
              </DialogContent>
            </Dialog>

            {/* Notification Permission Button */}
            {('Notification' in window) && !hideNotificationButton && ( // Only show if Notifications are supported and not hidden
              <Button
                variant="ghost"
                size="icon"
                className={`text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent ${notificationPermission === 'denied' ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={requestNotificationPermission}
                title={
                  notificationPermission === 'granted' ? "Notifications Enabled" :
                  notificationPermission === 'denied' ? "Notifications Disabled (Click to request again, may require browser settings change)" :
                  "Click to Enable Notifications"
                }
                disabled={notificationPermission === 'denied'} // Optionally disable if denied, though clicking might still be useful to trigger toast
              >
                {notificationPermission === 'granted' ? <BellRing className="h-5 w-5 text-green-500" /> : <BellOff className="h-5 w-5" />}
              </Button>
            )}

            {/* PiP Button */}
            {isPipSupported && (
              // Theme-aware PiP Button
              <Button variant="ghost" size="icon" className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent" onClick={togglePictureInPicture} title={isPipActive ? "Exit PiP" : "Enter PiP"}>
                {isPipActive ? <PictureInPicture2 className="h-5 w-5" /> : <PictureInPicture className="h-5 w-5" />}
              </Button>
            )}
          </div>
        </div>

        {/* Display current task if there is one */}
        {taskName && timerStatus !== "idle" && (
          <div className="mt-4 w-full text-sm dark:text-white/80 flex items-center gap-3 bg-purple-50/5 p-4 rounded-lg border border-purple-200/20 backdrop-blur-sm shadow-sm">
            <span className="text-lg flex-shrink-0">📝</span>
            <div className="flex flex-col flex-1 min-w-0">
              <div className="flex gap-2 items-center mb-1">
                <span className="text-xs text-muted-foreground dark:text-white/50">Current Task</span>
                {taskType && (
                  <span className="text-xs bg-purple-100/10 dark:bg-purple-900/30 px-2 py-0.5 rounded-full text-purple-700 dark:text-purple-300 font-medium">
                    {taskType}
                  </span>
                )}
              </div>
              <span className="font-medium truncate">{taskName}</span>
            </div>
          </div>
        )}

        {/* Pomodoro Phase Display (Use internal mode) */}
        {modeInternal === "pomodoro" && (
          <div className="flex items-center gap-2 text-muted-foreground dark:text-white/60 text-sm mt-2">
            <div className="flex items-center gap-1 bg-purple-50/5 px-3 py-1 rounded-full">
              <span>Session {completedSessions + 1}</span>
              <span>•</span>
              <span className="capitalize">{currentPhase.replace(/([A-Z])/g, ' $1').trim()}</span>
            </div>
          </div>
        )}
      </div>

      </div>
      {/* Timer Display (Use internal mode) */}
      <TimerDisplay
        mode={modeInternal}
        timeRemaining={modeInternal === "pomodoro" ? displayTime : 0}
        elapsedTime={modeInternal === "stopwatch" ? displayTime : 0}
      />

      {/* Timer Controls (Use internal mode) */}
      <TimerControls
        timerState={timerStatus}
        mode={modeInternal}
        onStart={startTimer}
        onPause={pauseTimer}
        onReset={() => resetTimer(true)} // Pass true to save potentially incomplete session
        onComplete={completeSession}
      />

      {/* Session Summary Dialog */}
      {renderSessionDialog()}
    </div>
  );
}